#ifndef _DMS_DISTRACTION_WARNING_BYD_H_
#define _DMS_DISTRACTION_WARNING_BYD_H_
#include <algorithm>
#include <chrono>
#include <cmath>
#include <deque>
#include <iostream>
#include <vector>
#include "HeadPoseCalibrator.h"
#include "dms_warning_interface.h"
namespace tongxing {
// 此类addw报警逻辑
class DistractionWarn final : public DistractionWarnInterface {
  public:
    DistractionWarn();
    ~DistractionWarn();

    //更新报警信息
    void Update(const Distraction_Info& info) override;

    //获取报警类型
    DistractionType GetWarnStatus() override;

    //重置所有报警
    void Reset() override;
    void ResetNofaceMixdata() override;
    //收到确认OK信号
    void SetOk() override;

    //自动标定
    TXDistracCaliStatus auto_calibration_distraction(TXDmsFaceInfo face_info,
                                                     const TXCarInfo* car_info,
                                                     const float leye_uper_curve_score,
                                                     const float reye_uper_curve_score,
                                                     long now_ts) override;

    void ClearCalibration() override;      //清除标定
    bool GetCalibrationStatus() override;  //获取标定状态
    bool IsDistracted(TXDmsFaceInfo face_info,
                      const TXCarInfo* car_info,
                      const distra_related_eyeinfo& related_eyeinfo,
                      bool is_face_keypoints_valid,
                      long now_ts) override;                             //判断是否分神状态
    void GetHeadPosePitch(float& min_value, float& max_value) override;  //获取头部姿态标定pitch角度
    void GetHeadPoseRoll(float& min_value, float& max_value) override;  //获取头部姿态标定后roll角度
    void GetHeadPoseYaw(float& min_value, float& max_value) override;
    std::string GetDistractReason() override { return distraction_reason; }
    std::string GetDistractParamers() override;

    void GetDistractionInfo(internal_analysis_distraction_info& info) override;
    bool GetMixDistraStatus(bool isFaceValid,
                            const float face_keypoints_score,
                            const TXCarInfo* car_info,
                            bool camera_occlusion,
                            bool distraction_status,
                            long now_ts) override;
    void getNonDistractHeadRotation(float& headyaw_min,
                                    float& headyaw_max,
                                    float& headpitch_min,
                                    float& headpitch_max) override;

    // 添加处理关键点数据的接口
    void SetFaceKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& face_keypoints);
    void SetEyeKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& eye_keypoints);
    void ProcessKeypoints(const std::shared_ptr<std::vector<cv::Point2f>>& face_keypoints,
                         const std::shared_ptr<std::vector<cv::Point2f>>& eye_keypoints,
                         long timestamp);

  private:
    static constexpr int kDistraSpeedThr = 20;
    static constexpr int kCalibrationSpeedThr = 20;

    static constexpr float kMinFaceScore = 0.5f;
    static constexpr float kFaceAngleThreshold = 0.35f;  // 人脸关键点阈值
    static constexpr float kMinEyeOpening = 0.3f;
static constexpr float kCurveThreshold = 0.80f;

    std::string last_distraction_reason = "";

    std::shared_ptr<HeadPoseCalibrator> calibrator;

    bool ok_flag = false;
    DistractionType histor_warn_type = DISTRACTION_NORMAL;  //历史结果

    //报警计时时间
    long alarm_start_time = 0;
    long alarm_end_time = 0;

    long alarm_ok_start_time = 0;
    long alarm_ok_end_time = 0;

    //缓存时间点
    long cache_time;
    std::deque<std::pair<long, int>> facevalid_mix_distra;
    std::deque<std::pair<long, bool>> distraction_short_result;  //短时状态数据队列
    std::deque<std::pair<long, bool>> distraction_long_result;   //长时状态数据队列
    std::deque<std::pair<long, bool>> distraction_3s_result;     //3s状态数据队列

    std::deque<std::pair<long, float>> head_yaw_3s_vec;  //存放头部yaw缓存数据
    std::deque<bool> glasses_vec;                        // 存放戴眼镜状态
    std::deque<bool> mask_vec;                        // 存放戴口罩状态
    // std::deque<float> temp_vec;

    internal_analysis_distraction_info distraction_info;  //分神分析信息（及用于json分析）
    long GetContinueDistractionTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取持续分神时间
    float GetContinueDistractionPercent(const std::deque<std::pair<long, bool>> distraction_deque,
                                        long time_gap);  //获取长时分神时间百分比
    long GetSumDistractionTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取累计分神时间
    long GetContinueFrontTime(
        const std::deque<std::pair<long, bool>> distraction_deque);  //获取持续正视前方时间

    bool GetResult(std::deque<std::pair<long, bool>>& distraction_deque, long time_gap);
    bool GetShortTimeResult(std::deque<std::pair<long, bool>>& distraction_deque,
                            long interval_times);

    bool GetDegradeResult2(std::deque<std::pair<long, bool>>& distraction_deque, int cnt);

    void GetDataQueue(std::deque<std::pair<long, bool>>& input_distraction_deque,
                      std::deque<std::pair<long, bool>>& out_distraction_deque,
                      long time_gap);
    void QuickFillingCaliData();

    //分神标定
    bool read_json = false;   // 读取json文件，本地调试模式
    bool log_switch = false;  //日志开关

    float headpose_yaw_left = 0.0f;
    float headpose_yaw_right = 0.0f;
    float headpose_pitch_up = 0.0f;
    float headpose_pitch_down = 0.0f;

    float headpose_spe_glasses_yaw_left = 0.0f;
    float headpose_spe_glasses_yaw_right = 0.0f;
    float headpose_spe_glasses_pitch_up = 0.0f;
    float headpose_spe_glasses_pitch_down = 0.0f;

    float headpose_yaw_normal_min = 0.0f;    //headpose正常yaw角度
    float headpose_yaw_normal_max = 0.0f;    //headpose正常yaw角度
    float headpose_pitch_normal_min = 0.0f;  //headpose正常pitch角度
    float headpose_pitch_normal_max = 0.0f;  //headpose正常pitch角度
    float headpose_roll_normal_min = 0.0f;   //headpose正常roll角度
    float headpose_roll_normal_max = 0.0f;   //headpose正常roll角度

    //视线往下绝对值
    float righteye_up_down_proportion = 0.8;
    float lefteye_up_down_proportion = 0.8;

    float head_yaw_bias = 0.0f;
    float head_yaw_3s_min = -99.0f;
    float head_yaw_3s_max = 99.0f;
    int head_yaw_bias_window_time = 3000;

    //标定角度参数
    float calibrate_headpose_yaw_min;
    float calibrate_headpose_yaw_max;
    float calibrate_headpose_pitch_min;
    float calibrate_headpose_pitch_max;
    float calibrate_headpose_roll_min;
    float calibrate_headpose_roll_max;

    //融合视线最值
    float right_gaze_vision_fusion_yaw_min;
    float right_gaze_vision_fusion_yaw_max;
    float right_gaze_vision_fusion_pitch_min;
    float right_gaze_vision_fusion_pitch_max;
    float left_gaze_vision_fusion_yaw_min;
    float left_gaze_vision_fusion_yaw_max;
    float left_gaze_vision_fusion_pitch_min;
    float left_gaze_vision_fusion_pitch_max;

    int window_time = 3000;       //分心时间窗
    float distraction_thr = 0.8;  //分心阈值

    float pitch_down = 0.0f;  //临时过滤低头误报闭眼pitch
    float pitch_up = 0.0f;    //临时过滤低头误报闭眼pitch
    float yaw_left = 0.0f;    //临时过滤偏头误报闭眼yaw 偏差值
    float yaw_right = 0.0f;   //临时过滤偏头误报闭眼yaw 偏差值
    float roll_left = 0.0f;   //临时过滤偏头误报分神roll
    float roll_right = 0.0f;  //临时过滤偏头误报分神roll

    float headpose_pitch_threshold;  //头部姿态pitch角度阈值
    float gaze_pitch_threshold;      //gaze pitch角度阈值

    float headpose_yaw_threshold;  //头部姿态yaw角度阈值
    float gaze_yaw_threshold;      //gaze yaw角度阈值

    float headgaze_yaw_l_offset;  //视线融合yaw 区间偏差值
    float headgaze_yaw_r_offset;  //视线融合yaw 区间偏差值

    int fusion_use_eye = 1;                              // 0:left eye 1:right eye 2:both eye
    int region_mapping_width = 800;                      //区域映射宽度
    int region_mapping_height = 800;                     //区域映射高度
    float tolerate_percentage = 0.005f;                  //容忍度百分比
    std::vector<std::vector<cv::Point2f>> region_hulls;  //区域轮廓点集
    int predict_result = 0;
    float mapping_x = 0.0f;
    float mapping_y = 0.0f;
    float gaze_pitch_mean = 0.0f;
    float gaze_yaw_mean = 0.0f;

    float headrange_center_yaw = 0.0f;
    float headrange_center_pitch = 0.0f;
    float headrange_a = 0.0f;
    float headrange_b = 0.0f;

    float steering_wheel_angle_min = 0.0f;  //方向盘转角最小值
    float steering_wheel_angle_max = 0.0f;  //方向盘转角最大值

    bool auto_calibration = false;  //分神自动标定状态
    bool is_eyegaze_exp = false; // 经验值判定视线是否分心
    long last_ts = 0;
    float temp_headpose_pitch_mean = -9999.0f;
    float temp_headpose_yaw_mean = -9999.0f;
    float temp_headpose_roll_mean = -9999.0f;

    float temp_lefteye_gaze_pitch_mean = -9999.0f;
    float temp_lefteye_gaze_yaw_mean = -9999.0f;
    float temp_righteye_gaze_pitch_mean = -9999.0f;
    float temp_righteye_gaze_yaw_mean = -9999.0f;

    float leye_uper_curve_score_mean_ = 1.0f;
    float reye_uper_curve_score_mean_ = 1.0f;
    bool is_leye_first_cali_success = true;
    bool is_reye_first_cali_success = true;
    static constexpr float KMaxCurveScore = 0.0035f;//0.003f;
    static constexpr float KMinCurveScore = 0.0001f;
    static constexpr float KMinFrontCurveScore = 0.0015f;
    static constexpr float KEyePitchOffsetBase = 1.5f;
    static constexpr float KEyePitchMaxOffset = 3.0f;

    static constexpr float kMinRollDiffer = 12.0f;  //8.0f;
    static constexpr float kMinPitchDiffer = 10.0f;
    static constexpr float kMinYawDiffer = 15.0f;
    static constexpr float kMinMappingYDiffer = 8.0f;
    bool has_head_cali = false;
    bool has_leye_cali = false;
    bool has_reye_cali = false;
    calistatus cali_status;
    bool cali_status_clear = false;

    TXEyeLandmark last_right_eye_landmark;
    TXEyeLandmark last_left_eye_landmark;
    int right_eye_lost_count = 0;
    int left_eye_lost_count = 0;

    // 关键点数据存储
    std::shared_ptr<std::vector<cv::Point2f>> current_face_keypoints_;
    std::shared_ptr<std::vector<cv::Point2f>> current_eye_keypoints_;
    long keypoints_timestamp_ = 0;

    // 眼角距离计算相关变量
    float left_eye_inner_outer_distance_ = 0.0f;   // 左眼内外眼角距离
    float right_eye_inner_outer_distance_ = 0.0f;  // 右眼内外眼角距离
    float inner_corners_distance_ = 0.0f;          // 左右眼内眼角距离

    void StartCalibration(const bool is_mask,
                          float head_pose_yaw,
                          float head_pose_pitch,
                          float head_pose_roll,
                          float gaze_left_yaw,
                          float gaze_left_pitch,
                          float gaze_right_yaw,
                          float gaze_right_pitch,
                          float left_eye_conf,
                          float right_eye_conf,
                          const float leye_uper_curve_score,
                          const float reye_uper_curve_score);  //开始标定

    float headpose_yaw_ = 0.0f;           //head pose yaw
    float headpose_pitch_ = 0.0f;         //head pose pitch
    float headpose_roll_ = 0.0f;          //head pose roll
    float gaze_left_eye_yaw_ = 1000.0f;   //gaze left eye yaw
    float gaze_left_eye_pitch_ = 0.0f;    //gaze left eye pitch
    float gaze_right_eye_yaw_ = 1000.0f;  //gaze right eye yaw
    float gaze_right_eye_pitch_ = 0.0f;   //gaze right eye pitch

    bool current_distraction_status = false;  //分神状态
    float face_box_ratio_ = -1.0f;
    
    bool region_init_flag = false;
    bool is_loop_calibration = true;
    //定义一个说明分神由headpose还是gaze触发，用于内部json保存使用
    std::string distraction_reason;
    //存放每一帧分神合理区间参数
    distraction_warning_param distract_param;

    // 因为头部姿态触发分心时，记录精确的分心原因
    std::string headpose_reason = "";
    struct GazeMappingResult {
        int predict_result;
        cv::Point2f mapping_point;
    };
    struct HeadRotationRange {
        float minyaw;    // 偏航角的最小值
        float maxyaw;    // 偏航角的最大值
        float minpitch;  // 俯仰角的最小值
        float maxpitch;  // 俯仰角的最大值
        float minroll;   // 翻滚角的最小值
        float maxroll;   // 翻滚角的最大值
    };

    typedef enum FuseEye {
        FUSE_EYE_LEFT = 0,
        FUSE_EYE_RIGHT = 1,
        FUSE_EYE_BOTH = 2,
    };

    typedef enum GazeDistractedType {
        GAZE_DISTRACTED_TYPE_NONE = 0,
        GAZE_DISTRACTED_TYPE_MODELNOTDET = 1,   // 模型未检测到眼睛
        GAZE_DISTRACTED_TYPE_INCREDIBLE = 2,    // 所需计算视线的眼睛信息不可靠
        GAZE_DISTRACTED_TYPE_UNDISTRACTED = 3,  // 未分心
        GAZE_DISTRACTED_TYPE_DISTRACTED = 4,    //分心
        GAZE_DISTRACTED_TYPE_DISTRACTED_WITH_NOTCURVE = 5,  //分心且有眼睛曲率判断因素
    };

    // 瞳孔位置状态枚举
    enum class PupilState {
        NORMAL = 0,      // 正常 - 瞳孔在中心区域内
        UNCERTAIN = 1,   // 其它/不确定 - 瞳孔在过渡区域
        DISTRACTED = 2   // 分心 - 瞳孔大幅偏离中心
    };

    // 左右眼瞳孔状态结构体
    struct EyesPupilState {
        PupilState left_eye_state;   // 左眼瞳孔状态
        PupilState right_eye_state;  // 右眼瞳孔状态
        
        EyesPupilState() : left_eye_state(PupilState::UNCERTAIN), right_eye_state(PupilState::UNCERTAIN) {}
        EyesPupilState(PupilState left, PupilState right) : left_eye_state(left), right_eye_state(right) {}
    };

    // 标定条件检查结果结构体
    struct CalibrationConditionResult {
        bool condition_met;              // 是否满足标定条件
        EyesPupilState pupil_state;      // 左右眼瞳孔状态
        bool right_eye_valid;            // 右眼距离是否有效
        bool left_eye_valid;             // 左眼距离是否有效
        bool inner_distance_valid;       // 内眼角距离是否有效
        bool ratio_valid;                // 近远眼比例是否有效
        bool far_eye_distance_valid;     // 远眼距离是否有效
        bool left_pupil_position_valid;  // 左眼瞳孔位置是否有效
        bool right_pupil_position_valid; // 右眼瞳孔位置是否有效
        bool pupil_position_valid;       // 瞳孔位置是否有效（根据fusion_eye综合判断）
        
        CalibrationConditionResult() : condition_met(false), pupil_state(), right_eye_valid(false), 
                                      left_eye_valid(false), inner_distance_valid(false), 
                                      ratio_valid(false), far_eye_distance_valid(false), 
                                      left_pupil_position_valid(false), right_pupil_position_valid(false),
                                      pupil_position_valid(false) {}
    };

    bool isFaceValid(const TXDmsFaceInfo& face_info);
    void updateHeadYawBias(const TXDmsFaceInfo& face_info, long now_ts);
    bool isSteeringAngleInvalid(const TXCarInfo* car_info, std::string& distraction_reason);
    bool isSpeGlassesSituation(const TXDmsFaceInfo& face_info);
    bool isSpeMaskSituation(const TXDmsFaceInfo& face_info);
    DistractionWarn::HeadRotationRange calcNonDistractHeadRotation(const TXCarInfo* car_info,
                                                                   const TXDmsFaceInfo& face_info);
    void calcOffsetFromSteeringAngle(const TXCarInfo* car_info,
                                     bool auto_calibration,
                                     float& headpose_yaw_l_offset,
                                     float& headpose_yaw_r_offset);
    bool checkFalseDistraction(TXDmsFaceInfo& face_info,
                               const HeadRotationRange& head_rotation_range,
                               std::string& distraction_reason);
    bool checkHeadPoseDistraction(const TXDmsFaceInfo& face_info,
                                  const HeadRotationRange& head_rotation_range,
                                  std::string& detailed_reason);
    void checkAbnormalDistraction(TXDmsFaceInfo& face_info,
                                  bool& current_distraction_status,
                                  std::string& headpose_reason,
                                  std::string& distraction_reason,
                                  float right_up_down_proportion,
                                  float left_up_down_proportion);
    std::pair<float, float> calcFusionRotationAngle(const bool is_mask,
                                                    const TXEyeLandmark& eye_landmark,
                                                    float cur_head_yaw,
                                                    float cur_head_pitch);
    DistractionWarn::GazeDistractedType calcPreMapping(const TXDmsFaceInfo& face_info,
                                                       float& fusion_yaw,
                                                       float& fusion_pitch,
                                                       float& cali_yaw,
                                                       float& cali_pitch);
    DistractionWarn::GazeDistractedType checkGazeDistraction(const TXDmsFaceInfo& face_info,
                                                             bool is_curve = true);
    bool distractionProcess(const TXDmsFaceInfo& face_info,
                            bool is_headpose_distracted,
                            GazeDistractedType gaze_result,
                            std::string& distraction_reason,
                            const float right_up_down_proportion,
                            const float left_up_down_proportion);
    void keepHistoryEyeGaze(TXEyeLandmark& eye_info,
                            TXEyeLandmark& last_eye_landmark,
                            int& eye_lost_count,
                            int eye_flag);
    bool isEyeCurve(const TXDmsFaceInfo face_info, const distra_related_eyeinfo& related_eyeinfo);
    
    // 计算眼角距离的函数
    void calculateEyeCornerDistances();
    CalibrationConditionResult isCalibrationConditionMet();
    
    // 瞳孔状态检查函数 - 支持三种状态判断（基于正常范围+四个方向的安全距离）
    // 返回左右眼的独立状态
    EyesPupilState checkPupilState(float normal_x_min, float normal_x_max, float normal_y_min, float normal_y_max,
                                   float safety_margin_left, float safety_margin_right, 
                                   float safety_margin_up, float safety_margin_down) const;
};

}  // namespace tongxing
#endif